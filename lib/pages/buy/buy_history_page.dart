import 'package:auto_route/annotations.dart';
import 'package:flutter/material.dart';
import 'package:fusion/gen/app.gaps.dart';
import 'package:fusion/gen/assets.gen.dart';
import 'package:fusion/gen/colors.gen.dart';
import 'package:fusion/pages/filter/model/filter_option.dart';
import 'package:fusion/pages/filter/model/filter_page_result.dart';
import 'package:fusion/pages/filter/widget/filter_entry_button.dart';
import 'package:fusion/pages/home/<USER>/goods_search_bar.dart';
import 'package:fusion/pages/order/model/trade_state.dart';
import 'package:fusion/router/app_router.dart';
import 'package:fusion/store/user_store.dart';
import 'package:fusion/utils/constants.dart';
import 'package:fusion/widgets/app_bar_back_icon.dart';
import 'package:fusion/widgets/empty_placeholder.dart';
import 'package:fusion/widgets/refresher/refresher.dart';
import 'package:fusion/widgets/text_filter/modal_text_filter_dialog.dart';
import 'package:fusion/widgets/text_filter/text_filter_bar.dart';
import 'package:fusion/widgets/text_filter/text_filter_entry.dart';
import 'package:fusion/widgets/text_filter/text_filter_option_data.dart';
import 'package:get/get.dart';

import '../order/widget/order_history_item.dart';
import 'controller/buy_history_controller.dart';

/// 购买记录
@RoutePage()
class BuyHistoryPage extends StatefulWidget {
  const BuyHistoryPage({
    super.key,
    this.asTab = false,
    this.tag,
  });

  /// 是否作为TabBar子页面。子页面无顶部导航栏，无需Scaffold
  final bool asTab;

  /// 页面标志。多个该页面的实例之间作为区分。若未指定使用[asTab]参数生成
  final String? tag;

  @override
  State<BuyHistoryPage> createState() => _BuyHistoryPageState();
}

class _BuyHistoryPageState extends State<BuyHistoryPage> {
  final stateKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    registerControllerServiceEntry();
  }

  /// 注册重置入口
  void registerControllerServiceEntry() {
    final service = UserStore.to.resetService;

    service.registerControllerServiceEntry('BuyHistoryRoute',
        [service.generateEntry<BuyHistoryController>(tag: _buildTag())]);
  }

  _switchOrderState(BuyHistoryController controller) async {
    final ret = await showDialog(
      context: context,
      useSafeArea: false,
      routeSettings: const RouteSettings(name: "BuyHistoryStateFilterDialog"),
      builder: (c) => ModalTextFilterDialog<TradeState>(
        anchor: stateKey,
        options: TradeState.values
            .map(
              (e) => TextFilterOptionData(
                value: e,
                title: e.filterTitle,
                highlight: controller.tradeState.value == e,
              ),
            )
            .toList(),
      ),
    );
    if (ret != null && ret is TradeState) {
      controller.updateTradeState(ret);
    }
  }

  @override
  Widget build(BuildContext context) {
    final body = _buildBody(context);
    if (widget.asTab) {
      return body;
    }
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: AppBar(
        leading: const AppBarBackIcon(),
        title: const Text("我的出售"),
      ),
      body: body,
    );
  }

  Widget _buildBody(BuildContext context) {
    return GetBuilder(
      init: BuyHistoryController(),
      tag: _buildTag(),
      builder: (controller) {
        final refresher = _buildRefresher(context, controller);
        return Column(
          children: [
            GoodsSearchBar(
              keywords: controller.keywords,
              padding: const EdgeInsets.only(
                left: Constants.horizontalPadding,
                bottom: 5,
                top: 5,
              ),
              onTapInput: () async {
                final key = await AppRouter.to.push<String>(KeyWordsFilterRoute(
                  previousKeywords: controller.keywords.value,
                ));
                if (key != null) {
                  controller.updateKeywords(key);
                }
              },
              onClear: () {
                controller.updateKeywords(null);
              },
              actions: [
                FilterEntryButton(
                  hasFilters: controller.hasFilters,
                  onPressed: () async {
                    final result = await AppRouter.to.push<FilterPageResult>(
                      FilterRoute(
                        previousResult: controller.filters,
                        supported: const {
                          FilterOption.category,
                          FilterOption.quality,
                          FilterOption.exterior,
                          FilterOption.rarity,
                          FilterOption.color,
                        },
                      ),
                    );
                    if (result != null) {
                      controller.updateFilterResult(result);
                    }
                  },
                ),
              ],
            ),
            Gaps.v8,
            Expanded(
              child: Container(
                decoration: const BoxDecoration(
                  color: AppColors.pageBackground,
                  borderRadius: BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: Column(
                  children: [
                    TextFilterBar(
                      children: [
                        Obx(
                          () => TextFilterEntry(
                            key: stateKey,
                            text: controller.tradeState.value == TradeState.none
                                ? "订单状态"
                                : controller.tradeState.value.filterTitle,
                            padding: const EdgeInsets.symmetric(vertical: 10),
                            highlight:
                                controller.tradeState.value != TradeState.none,
                            onTap: () {
                              _switchOrderState(controller);
                            },
                          ),
                        ),
                      ],
                    ),
                    Expanded(child: refresher)
                  ],
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildRefresher(
      BuildContext context, BuyHistoryController controller) {
    return Obx(() {
      return Refresher(
        key: ValueKey(controller.refresherKey.value),
        controller: controller.refreshController,
        empty: controller.isEmpty,
        firstLoad: controller.isFirstLoad,
        onRefresh: controller.onRefresh,
        onLoading: controller.onLoadMore,
        builder: (c) {
          return ListView.separated(
            padding: const EdgeInsets.symmetric(
              horizontal: Constants.horizontalPadding / 2,
            ),
            itemCount: controller.length,
            itemBuilder: (c, index) {
              final item = controller.itemAt(index);
              return GestureDetector(
                onTap: () {
                  AppRouter.to
                      .push(OrderDetailRoute(orderId: item.order.orderId!));
                },
                child: OrderHistoryItem(
                  data: item,
                  onExpired: controller.onRefreshIfNeeds,
                  onAction : (data, action) {
                    
                  },
                ),
              );
            },
            separatorBuilder: (context, index) => Gaps.v12,
          );
        },
        emptyBuilder: (ctx) {
          return controller.hasConditions
              ? const EmptyPlaceholder(message: "未找到对应的购买记录")
              : EmptyPlaceholder(
                emptyIcon: Assets.icon.emptySellList,
                message: "暂无购买记录"
              );
        },
      );
    });
  }

  String _buildTag() {
    return widget.tag != null ? widget.tag! : "asTab:${widget.asTab}";
  }
}
